from typing import Optional
from app.api.schemas.project_configs import ProjectConfigResearch, ProjectConfigResponse2, ProjectConfigResponse, LiteratureLibraryType, UpdateEstimatedTimeProcessingMode
from app.models.project_configs import ProjectConfig
from app.models.project_leaders import ProjectLeader
from app.services.llm_service import call_llm
from app.services.prompts import get_leader_ai_introduction_prompt, AI_INTRODUCTION_LEADER_SYSTEM_PROMPT
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.utils.enum import ProjectConfigError, CallLLMFlag, ProjectLeaderError
from datetime import datetime, timedelta
from app.models.project_member_joins import ProjectMemberJoin
from app.api.schemas.project_member_joins import ProjectMemberJoinResponse
from app.utils.utils import (
  extract_citations_in_paper_by_sentence, 
  extract_reference_item_list, 
  read_file_content, 
  split_by_citation_section, 
  split_markdown_text_by_paragraph
)
from pydantic import UUID4 as UUID
from app.api.repository.user_default_model import get_user_model
from app.models.organization_model_use import UseCase


async def get_research_by_config_id(config_id: UUID) -> Optional[ProjectConfigResearch]:
    """
    通过config_id查询关联的research信息
    
    Args:
        config_id: 项目配置ID
        
    Returns:
        Optional[ResearchResponse]: 如果找到则返回research信息，否则返回None
    """
    # 首先查询project_config确认research_id
    project_config = await ProjectConfig.get_or_none(
        id=config_id,
        is_deleted=0
    ).prefetch_related('research')
    
    if not project_config or not project_config.research:
        return None
    
    
    # 构建响应数据
    return ProjectConfigResearch.model_validate(project_config)
logger = get_logger(__name__)

async def get_one_project_config(
  project_id: str
) -> ProjectConfigResponse2:
    """获取特定项目配置详情（管理员接口）"""
    config = await ProjectConfig.filter(id=project_id, is_deleted=0).prefetch_related(
        # 关联材料主体
        "leader",
        # 关联用户
        "user",
        # 关联模型表
        "model",
        # 关联材料主体的省市区
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        # 关联用户的角色
        "user__role",
        # 关联用户的组织
        "user__organization",
    ).first()
    if not config:
        raise Exception(ProjectConfigError.NOT_RECORD)
    config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
    # 获取所有相关的团队成员
    team_members = []
    if config.team_members:
        team_members = await ProjectMemberJoin.filter(
            join_id=config.team_members,
            is_deleted=0
        ).prefetch_related("member").all()
      
    config_dict["team_members"] = [
        ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
        for tm in team_members
    ]
    logger.info(f"get_one_project_config:项目 {config.id} 获取项目配置详情完成: {config.estimated_time}")
    return ProjectConfigResponse2.model_validate(config_dict)

async def generate_leader_ai_introduction(
    leader_id: UUID,
    name: str,
    current_user: UserResponse
) -> str:
    """
    生成团队的 AI 介绍
    
    Args:
        leader_id: 当前主体的id,
        name: 研究主题 
        
    Returns:
        str: 生成的团队 AI 介绍文本
    """
    try:
        # 获取项目配置
        leader = await ProjectLeader.filter(id=leader_id, is_deleted=0).first()
        
        if not leader:
            raise Exception(ProjectLeaderError.NOT_RECORD.value)
        if not name:
            raise Exception(ProjectConfigError.NAME_EMPTY.value)
        prompt = get_leader_ai_introduction_prompt(
            name=leader.name or "",
            achievements=leader.related_projects or "",
            title=name or "",
            code=leader.credit_code or ""
        )
        try:
            model_config = await get_user_model(
                current_user=current_user,
                use_case=UseCase.PROJECT_CONFIG_NEED.value
            )
        except Exception as e:
            raise Exception(e)
        
        # 调用大模型生成介绍
        messages = [
            {
                "role": "system",
                "content": AI_INTRODUCTION_LEADER_SYSTEM_PROMPT
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        response = await call_llm(
            messages=messages,
            flag=CallLLMFlag.LEADER_AI_INTRODUCTION,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url,
            model=model_config.model_name
            # max_tokens=500
        )
    except Exception as e:
        logger.error(f"生成材料主体AI介绍时出错: {str(e)}")
        raise Exception(f"生成材料主体AI介绍失败: {str(e)}")
    return response
async def save_leader_ai_introduction(
    introduction: str,
    project_id: UUID
):
    project_config = await ProjectConfig.filter(id=project_id, is_deleted=0).first()
    if not project_config:
        raise Exception(ProjectConfigError.NOT_RECORD)
    project_config.ai_leader_introduction = introduction
    project_config.updated_at = datetime.now()
    await project_config.save()
    return await get_one_project_config(project_id)


async def calculate_generate_outline_time(project_config: ProjectConfig) -> datetime:
    """
    计算生成大纲的预估时间
    
    计算规则：
    - 生成大纲固定时间为1分钟
    
    Args:
        project_config: 项目配置对象

    Returns:
        datetime: 预估完成时间
    """
    estimated_time = datetime.now() + timedelta(minutes=1)
    logger.info(f"calculate_generate_outline_time:项目 {project_config.id} 生成大纲预估时间计算完成: {estimated_time}")
    return estimated_time

async def calculate_estimated_time(project_config: ProjectConfig) -> datetime:
    """
    计算项目预估时间

    Args:
        project_config: 项目配置对象

    Returns:
        datetime: 预估完成时间
    """
    # 基础时间（分钟）
    base_minutes = 0

    # 根据字数计算基础时间
    word_count = project_config.word_count_requirement or 0
    if word_count < 5000:  # 少于5k字
        base_minutes = 5  # 5k字：5分钟
    elif 5000 <= word_count < 10000:  # 5k~10k字
        base_minutes = 5  # 5k字：5分钟
    elif 10000 <= word_count < 15000:  # 1w~1.5w字
        base_minutes = 6  # 1w字：6分钟
    elif 15000 <= word_count < 20000:  # 1.5w~2w字
        base_minutes = 7  # 1.5w字：7分钟
    elif 20000 <= word_count < 25000:  # 2w~2.5w字
        base_minutes = 8  # 2w字：8分钟
    elif 25000 <= word_count < 30000:  # 2.5w~3w字
        base_minutes = 9  # 2.5w字：9分钟
    elif 30000 <= word_count < 35000:  # 3w~3.5w字
        base_minutes = 10  # 3w字：10分钟
    elif 35000 <= word_count < 40000:  # 3.5w~4w字
        base_minutes = 11  # 3.5w字：11分钟
    elif 40000 <= word_count < 45000:  # 4w~4.5w字
        base_minutes = 12  # 4w字：12分钟
    elif 45000 <= word_count < 50000:  # 4.5w~5w字
        base_minutes = 13  # 4.5w字：13分钟
    else:  # 5w字以上
        base_minutes = 14  # 5w字：14分钟

    # 附件时间：每个附件增加1分钟
    attachments_count = len(project_config.requirements_attachments or [])
    attachment_minutes = attachments_count * 1

    # 文献库时间（多选）
    literature_minutes = 0
    if project_config.literature_library:
        # 按逗号分割多个文献库类型
        lit_types = [lit_type.strip() for lit_type in project_config.literature_library.split(',') if lit_type.strip()]
        
        for lit_type in lit_types:
            if lit_type == LiteratureLibraryType.PUBMED.value:
                literature_minutes += 2  # PubMed增加2分钟
            elif lit_type in [LiteratureLibraryType.CELL.value, LiteratureLibraryType.NATURE.value, LiteratureLibraryType.SCIENCE.value]:
                literature_minutes += 1  # Cell、Nature、Science增加1分钟
        
        logger.info(f"calculate_estimated_time:项目 {project_config.id} 文献库时间计算: 选择的文献库={lit_types}, 总时间={literature_minutes}分钟")

    # 总时间（分钟）
    total_minutes = base_minutes + attachment_minutes + literature_minutes

    # 计算预估完成时间（从当前时间开始）
    estimated_time = datetime.now() + timedelta(minutes=total_minutes)
    logger.info(f"calculate_estimated_time:项目 {project_config.id} 生成正文预估时间计算完成: {estimated_time}")
    return estimated_time

async def calculate_hallucination_check_time(project_config: ProjectConfig) -> datetime:
    """
    计算幻觉审查处理的预估时间
    
    计算规则：
    - 1个角标幻觉审查时间为5秒
    - 1篇参考文献幻觉审查时间为10秒

    Args:
        project_config: 项目配置对象

    Returns:
        datetime: 预估完成时间
    """
    # 报告内容
    report_content = project_config.manual_modified_report or project_config.ai_generated_report
    result_content = read_file_content(report_content)
    
    # 将原文分成左侧文本，右侧文本和参考文献标题文本
    origin_data = split_by_citation_section(result_content)
    sentences = extract_citations_in_paper_by_sentence(origin_data.text_before_citation_title) # 正文角标合计
    references = extract_reference_item_list(origin_data.citation_paragraph_without_title) # 参考文献合计
    
    # 计算预估时间（以秒为单位）
    # 正文角标数量：1个角标 = 5秒
    citation_seconds = len(sentences) * 5
    # 参考文献数量：1篇参考文献 = 10秒
    reference_seconds = len(references) * 10
    
    # 总时间（秒）
    total_seconds = citation_seconds + reference_seconds
    
    estimated_time = datetime.now() + timedelta(seconds=total_seconds)
    logger.info(f"calculate_hallucination_check_time:项目 {project_config.id} 幻觉审查预估时间计算完成: 角标数量={len(sentences)}({citation_seconds}秒), 参考文献数量={len(references)}({reference_seconds}秒), 总时间={total_seconds}秒, 预估完成时间={estimated_time}")
    
    return estimated_time

async def calculate_ai_trace_removal_time(project_config: ProjectConfig) -> datetime:
    """
    计算AI去痕处理的预估时间
    
    计算规则：
    - AI去痕：正文字数*0.00023分钟+段落数*0.1分钟=AI去痕时间（向正无穷取整）
    
    Args:
        project_config: 项目配置对象

    Returns:
        datetime: 预估完成时间
    """
    import math
    
    # 报告内容
    report_content = project_config.manual_modified_report or project_config.ai_generated_report
    if not report_content:
        # 如果没有报告内容，使用默认时间1分钟
        estimated_time = datetime.now() + timedelta(minutes=1)
        logger.info(f"calculate_ai_trace_removal_time:项目 {project_config.id} 无报告内容，使用默认时间: {estimated_time}")
        return estimated_time
    
    try:
        # 读取报告文件内容
        result_content = read_file_content(report_content)
        sections = split_markdown_text_by_paragraph(result_content)
        
        # 计算正文字数（不包括空格和标点，只计算字母数字和中文字符）
        text_content = result_content.replace(' ', '').replace('\n', '').replace('\t', '')
        word_count = len([c for c in text_content if c.isalnum() or ord(c) > 127])  # 只计算字母数字和中文字符
        
        # 计算AI去痕时间：正文字数*0.00023分钟 + 段落数*0.1分钟
        word_based_minutes = word_count * 0.00023
        section_minutes = len(sections) * 0.1
        
        # 总时间 = 正文字数时间 + 段落时间
        total_minutes = word_based_minutes + section_minutes
        
        # 向正无穷取整（向上取整）
        total_minutes = math.ceil(total_minutes)
        
        estimated_time = datetime.now() + timedelta(minutes=total_minutes)
        logger.info(f"calculate_ai_trace_removal_time:项目 {project_config.id} AI去痕预估时间计算完成: 正文字数={word_count}({word_based_minutes:.4f}分钟), 段落数量={len(sections)}({section_minutes}分钟), 总时间={total_minutes}分钟（向上取整）, 预估完成时间={estimated_time}")
        
        return estimated_time
    except Exception as e:
        # 如果计算失败，使用默认时间1分钟
        logger.error(f"calculate_ai_trace_removal_time:项目 {project_config.id} AI去痕预估时间计算失败: {str(e)}，使用默认时间")
        estimated_time = datetime.now() + timedelta(minutes=1)
        return estimated_time

async def update_estimated_time(project_config: ProjectConfig, processing_mode: UpdateEstimatedTimeProcessingMode = UpdateEstimatedTimeProcessingMode.GENERATE_CONTENT) -> datetime:
    """
    更新项目的预估时间

    Args:
        project_config: 项目配置

    Returns:
        datetime: 更新后的预估时间
    """
    # 查询项目配置
    if not project_config:
        raise Exception(ProjectConfigError.NOT_RECORD)
    
    # 记录保存前的状态
    logger.info(f"项目 {project_config.id} 保存前的estimated_time: {project_config.estimated_time}")
    
    estimated_time = None
    if processing_mode == UpdateEstimatedTimeProcessingMode.GENERATE_OUTLINE: # 生成大纲计算预估时间
        logger.info(f"项目 {project_config.id} 生成大纲预估时间重新计算开始: {processing_mode}")
        estimated_time = await calculate_generate_outline_time(project_config)
        logger.info(f"项目 {project_config.id} 生成大纲预估时间重新计算完成: {estimated_time}")
    elif processing_mode == UpdateEstimatedTimeProcessingMode.GENERATE_CONTENT:# 生成正文计算预估时间
        logger.info(f"项目 {project_config.id} 生成正文预估时间重新计算开始: {processing_mode}")
        # 计算预估时间
        estimated_time = await calculate_estimated_time(project_config)
        logger.info(f"项目 {project_config.id} 生成正文预估时间重新计算完成: {estimated_time}")
    elif processing_mode == UpdateEstimatedTimeProcessingMode.HALLUCINATION_CHECK: # 幻觉审查计算预估时间
        logger.info(f"项目 {project_config.id} 幻觉审查预估时间重新计算开始: {processing_mode}")
        estimated_time = await calculate_hallucination_check_time(project_config)
    elif processing_mode == UpdateEstimatedTimeProcessingMode.AI_TRACE_REMOVAL: # AI去痕计算预估时间
        logger.info(f"项目 {project_config.id} AI去痕预估时间重新计算开始: {processing_mode}")
        estimated_time = await calculate_ai_trace_removal_time(project_config)
    
    logger.info(f"项目 {project_config.id} 预估时间重新计算完成estimated_time: {estimated_time}")
    return estimated_time