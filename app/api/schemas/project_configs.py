from pydantic import BaseModel, UUID4, <PERSON>
from typing import Optional, List
from datetime import datetime, timezone
from app.api.schemas.project_leaders import ProjectLeaderResponse
from app.api.schemas.project_member_joins import ProjectMemberJoinResponse
from app.api.schemas.user import UserResponse
from enum import Enum
from app.api.schemas.model_config import ModelConfigResponse
# from app.models.research import ResearchStatus
from app.api.schemas.research import ResearchResponse


# class ApplicationCategory(str, Enum):
#   NSFC = "NSFC"  # 国自然基金-面上项目
# class ApplicationCategoryText(str, Enum):
#   NSFC = "国自然基金"


class LanguageStyle(str, Enum):
  """语言风格枚举"""

  """专业严谨"""
  PROFESSIONAL_RIGOROUS = "PROFESSIONAL_RIGOROUS"
  """正式规范"""
  FORMAL_NORMS = "FORMAL_NORMS"
  """学术权威"""
  ACADEMIC_AUTHORITY = "ACADEMIC_AUTHORITY"

class UpdateEstimatedTimeProcessingMode(str, Enum):
  """处理模式枚举"""
  """重新计算预估时间"""
  
  """生成大纲"""
  GENERATE_OUTLINE = "GENERATE_OUTLINE"
  """生成正文"""
  GENERATE_CONTENT = "GENERATE_CONTENT"
  """幻觉审查"""
  HALLUCINATION_CHECK = "HALLUCINATION_CHECK"
  """AI去痕"""
  AI_TRACE_REMOVAL = "AI_TRACE_REMOVAL"
  
class LanguageStyleText(str, Enum):
  PROFESSIONAL_RIGOROUS = "专业严谨"
  FORMAL_NORMS = "正式规范"
  ACADEMIC_AUTHORITY = "学术权威"

class LanguageStylePrompt(str, Enum):
  PROFESSIONAL_RIGOROUS = "Language style, default professional and rigorous"
  FORMAL_NORMS = "Language style, default formal norms"
  ACADEMIC_AUTHORITY = "Language style, default academic authority"

class ProjectConfigStatus(str, Enum):
  """项目配置状态枚举"""
  """配置中"""
  CONFIGURING = "CONFIGURING"
  """生成大纲中"""
  OUTLINE_GENERATING = "OUTLINE_GENERATING"
  """大纲生成完成"""
  OUTLINE_GENERATED = "OUTLINE_GENERATED"
  """大纲生成失败"""
  OUTLINE_FAILED = "OUTLINE_FAILED"
  """大纲生成被终止"""
  OUTLINE_CANCELED = "OUTLINE_CANCELED"
  """报告生成中"""
  REPORT_GENERATING = "REPORT_GENERATING"
  """报告生成完成"""
  REPORT_GENERATED = "REPORT_GENERATED"
  """报告生成失败"""
  REPORT_FAILED = "REPORT_FAILED"
  """报告生成被终止"""
  REPORT_CANCELED = "REPORT_CANCELED"
  """去除幻觉中"""
  REMOVE_HALLUCINATING = "REMOVE_HALLUCINATING"
  """去除幻觉完成"""
  REMOVE_HALLUCINATED = "REMOVE_HALLUCINATED"
  """去除AI追踪中"""
  REMOVE_AI_TRACING = "REMOVE_AI_TRACING"
  """去除AI追踪完成"""
  REMOVE_AI_TRACED = "REMOVE_AI_TRACED"

def get_status_order(status: str):
  """流程顺序"""
  amap = {
    "CONFIGURING": 0,
    "OUTLINE_GENERATING": 10,
    "OUTLINE_GENERATED": 20,
    "OUTLINE_FAILED": 20,
    "OUTLINE_CANCELED": 20,
    "REPORT_GENERATING": 30,
    "REPORT_GENERATED": 40,
    "REPORT_FAILED": 40,
    "REPORT_CANCELED": 40,
    "REMOVE_HALLUCINATING": 50,
    "REMOVE_HALLUCINATED": 60,
    "REMOVE_AI_TRACING": 70,
    "REMOVE_AI_TRACED": 80
  }
  return amap[status]

class LiteratureLibraryType(str, Enum):
  """文献库类型枚举"""
  """PubMed医学文献库"""
  PUBMED = "PUBMED"
  """Cell期刊"""
  CELL = "CELL"
  """Nature期刊"""
  NATURE = "NATURE"
  """Science期刊"""
  SCIENCE = "SCIENCE"
  @classmethod
  def get_url(cls, library_type: str) -> str:
    """获取文献库对应的URL"""
    url_map = {
      cls.CELL.value: "www.cell.com",
      cls.NATURE.value: "www.nature.com",
      cls.SCIENCE.value: "www.science.org"
    }
    return url_map.get(library_type, "")

class LiteratureLibraryTypeText(str, Enum):
  """文献库类型枚举"""
  """PubMed医学文献库"""
  PUBMED = "PubMed"
  """Cell期刊"""
  CELL = "Cell"
  """Nature期刊"""
  NATURE = "Nature"
  """Science期刊"""
  SCIENCE = "Science"
  

class ProjectConfigBase(BaseModel):
    name: Optional[str] = None
    application_category: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    status: Optional[str] = None
    team_introduction: Optional[str] = None
    ai_leader_introduction: Optional[str] = None
    estimated_time: Optional[datetime] = None
    user_add_demo_id: Optional[UUID4] = None
    user_add_prompt: Optional[str] = None

class LanguageStyleResponse(BaseModel):
   value: LanguageStyle
   label: str

class LiteratureLibraryResponse(BaseModel):
   value: LiteratureLibraryType
   label: str

class ProjectConfigCreate(BaseModel):
    name: Optional[str] = None
    application_category: Optional[str] = None
    leader: Optional[str] = None
    research: Optional[str] = None
    team_members: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    team_introduction: Optional[str] = None
    model: Optional[str] = None
    requirements_attachments_id: Optional[List[UUID4]] = None
    ai_leader_introduction: Optional[str] = None
    estimated_time: Optional[datetime] = None
    url_ids: Optional[List[UUID4]] = None
    user_add_demo_id: Optional[UUID4] = None
    user_add_prompt: Optional[str] = None

class ProjectConfigResponse(BaseModel):
    id: UUID4
    name: Optional[str] = None
    application_category: Optional[str] = None  
    leader: Optional[ProjectLeaderResponse] = None
    user: Optional[UserResponse] = None
    team_members: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    ai_generated_outline: Optional[str] = None
    manual_modified_outline: Optional[str] = None
    ai_generated_report: Optional[str] = None
    report_generation_time: Optional[datetime] = None
    outline_generated_time: Optional[datetime] = None
    manual_modified_outline_time: Optional[datetime] = None
    manual_modified_report_time: Optional[datetime] = None
    outline_tokens_consumed: Optional[int] = None
    report_tokens_consumed: Optional[int] = None
    outline_generated_time: Optional[datetime] = None
    manual_modified_report: Optional[str] = None
    status: Optional[ProjectConfigStatus] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: int = 0
    deleted_at: Optional[datetime] = None
    outline_tokens_consumed: Optional[int] = None
    report_tokens_consumed: Optional[int] = None
    team_introduction: Optional[str] = None
    model: Optional[ModelConfigResponse] = None
    research_id: Optional[UUID4] = None
    ai_trace_report: Optional[str] = None
    ai_trace_generated_time: Optional[datetime] = None
    hallucination_generated_time: Optional[datetime] = None
    hallucination_report: Optional[str] = None
    ai_leader_introduction: Optional[str] = None
    estimated_time: Optional[datetime] = None
    user_add_demo_id: Optional[UUID4] = None
    user_add_prompt: Optional[str] = None
    model_config = {
      "from_attributes": True
    }
class ProjectConfigResponse2(BaseModel):
    id: UUID4
    name: Optional[str] = None
    application_category: Optional[str] = None  
    leader: Optional[ProjectLeaderResponse] = None
    user: Optional[UserResponse] = None
    team_members: Optional[List[ProjectMemberJoinResponse]] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    ai_generated_outline: Optional[str] = None
    manual_modified_outline: Optional[str] = None
    manual_modified_report: Optional[str] = None
    ai_generated_report: Optional[str] = None
    report_generation_time: Optional[datetime] = None
    outline_generated_time: Optional[datetime] = None
    status: Optional[ProjectConfigStatus] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_deleted: int = 0
    deleted_at: Optional[datetime] = None
    outline_tokens_consumed: Optional[int] = None
    report_tokens_consumed: Optional[int] = None
    team_introduction: Optional[str] = None
    model: Optional[ModelConfigResponse] = None
    requirements_attachments_id: Optional[List[UUID4]] = None
    research_id: Optional[UUID4] = None
    ai_trace_report: Optional[str] = None
    ai_trace_generated_time: Optional[datetime] = None
    hallucination_generated_time: Optional[datetime] = None
    hallucination_report: Optional[str] = None
    ai_leader_introduction: Optional[str] = None
    estimated_time: Optional[datetime] = None
    user_add_demo_id: Optional[UUID4] = None
    user_add_prompt: Optional[str] = None


    model_config = {
      "from_attributes": True
    }

class ProjectConfigUpdate(BaseModel):
    name: Optional[str] = None
    application_category: Optional[str] = None
    leader: Optional[str] = None
    research: Optional[str] = None
    team_members: Optional[str] = None
    word_count_requirement: Optional[int] = None
    literature_library: Optional[str] = None
    requirements_attachments: Optional[List[str]] = None
    language_style: Optional[str] = None
    team_introduction: Optional[str] = None
    # ai_generated_outline: Optional[str] = None
    # manual_modified_outline: Optional[str] = None
    # ai_generated_report: Optional[str] = None
    # report_generation_time: Optional[datetime] = None
    status: Optional[ProjectConfigStatus] = None
    is_deleted: Optional[int] = None
    deleted_at: Optional[datetime] = None 
    model: Optional[UUID4] = None
    requirements_attachments_id: Optional[List[UUID4]] = None
    ai_leader_introduction: Optional[str] = None
    url_ids: Optional[List[UUID4]] = None
    user_add_demo_id: Optional[str] = None
    user_add_prompt: Optional[str] = None
class ProjectConfigResearch(BaseModel):
  status: ProjectConfigStatus
  ai_generated_report: Optional[str] = None
  manual_modified_report: Optional[str] = None
  research: Optional[ResearchResponse] = None
  model_config = {
    "from_attributes": True
  }
class GenerateConfig(BaseModel):
  summary_literature: Optional[bool] = Field(default=None, description="是否开启文献总结")
class GenerateLeaderAI(BaseModel):
  leader_id: UUID4 = Field(..., description="材料主体的ID")
  name: str = Field(..., description="研究主题")

class ProjectConfigDemoResponse(BaseModel):
    """AI去痕完成状态项目的demo响应模型"""
    id: UUID4
    name: Optional[str] = None
    word_count: Optional[int] = None  # AI去痕报告字数（去除空白字符）
    paragraph_count: Optional[int] = None  # 段落数（title_head_match识别的markdown标题数量）
    ai_trace_minutes: Optional[float] = None  # AI去痕耗时（分钟）
    content: Optional[str] = None  # AI去痕报告的完整内容
    
    model_config = {
        "from_attributes": True,
        "json_encoders": {
            datetime: lambda v: v.replace(tzinfo=timezone.utc).isoformat() if v else None,
        },
    }

class ProjectCategoryModule(BaseModel):
    """项目分类模块"""
    key: str = Field(..., description="模块")
    title: str = Field(..., description="模块标题")

class ProjectCategory(BaseModel):
    """项目分类"""
    key: str = Field(..., description="分类")
    title: str = Field(..., description="分类标题")
    modules: List[ProjectCategoryModule] = Field(..., description="模块列表")