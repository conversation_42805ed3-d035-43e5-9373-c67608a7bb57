from fastapi import APIRouter
from typing import Optional, Callable
from app.api.repository.project_config import update_estimated_time
from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
from datetime import datetime
import os
import asyncio
import json
from app.api.deps import get_current_user_from_state
from app.models.project_configs import ProjectConfig
from app.api.schemas.project_configs import ProjectConfigResponse2, ProjectConfigStatus, GenerateConfig, UpdateEstimatedTimeProcessingMode
from app.api.schemas.project_members import ProjectMemberBase
from app.services.prompts import generate_outline_prompt, OUTLINE_SYSTEM_PROMPT
from app.utils.llm_service import stream_llm_and_save
from app.utils.utils import (
    send_data,
    ResponseModel,
    stream_file_content_sse,
    handle_before_save,
    save_text_to_file,
    sanitize_filename,
    ContentStatus,
    stream_handle_before_send,
    remove_markdown_h1_and_text,
    docx_file_to_markdown,
    read_file_content
)
from app.api.routes.project_configs import get_one_project_config
from app.utils.content_manager import ContentManager, Data
from app.services.llm_token_service import get_generation_result
from app.models.research import Research, ResearchStatus
from app.api.repository.project_report import stream_research_report_content
from app.core.config import settings
from app.core.logging import get_logger
from app.models.user_report_usage import UserReportUsage
from app.api.repository.user_report_usage import check_user_usage_limit
from app.api.routes.workflow import create_workflow
from app.api.schemas.role import InsetRole
from app.models.user import User
from app.utils.enum import CallLLMFlag
from app.api.schemas.user import UserResponse
from starlette.background import BackgroundTask
from app.api.repository.upload_file import get_file_content_by_id
from app.utils.enum import ProjectConfigError

# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

outline_content_manager = ContentManager()
report_content_manager = ContentManager()

# 流式返回
def stream_fn(text):
    yield f"data: {json.dumps({'content': text, 'status': ContentStatus.NORMAL})}\n\n"
        # 发送完成事件
    yield f"data: {json.dumps({'status': 'completed'})}\n\n"
#返回体验用户看到的文本
def send_trail_text(project_id: str, flag: str):
    data = report_content_manager.get_project_content(project_id) if flag == 'report' else outline_content_manager.get_project_content(project_id)
    if data:
        list_data = data.read_chunks + data.unread_chunks
        content = ""
        for item in list_data:
            if item.status == ContentStatus.NORMAL and len(content) <= settings.TRIAL_USER_MAX_TEXT:
                content += item.content
        if len(content) >= settings.TRIAL_USER_MAX_TEXT:
            return stream_handle_before_send(content)
        else:
            return ""
    else:
        return ""
def calculate_length(chunk: Data):
    if chunk.status == ContentStatus.NORMAL:
        return len(chunk.content) if chunk.content else 0
    return 0
def yield_self(
    chunk: Data,
    send_length: int,
    current_user: UserResponse
):
    if send_length >= settings.TRIAL_USER_MAX_TEXT and current_user.is_trial:
        return f"data: {json.dumps({'status': 'completed'})}\n\n"
    else:
        return f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
    
async def remove_storage(project_id: str, flag='outline'):
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("leader", "user", "model").first()
    if flag == 'outline':
        # 获取该项目的内容对象
        project_content = outline_content_manager.get_project_content(project_id)
            
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                # error = f"已取消大纲生成任务"
                logger.info(f"已取消项目 {project_id} 的大纲生成任务")
                # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
            except Exception as e:
                error = f"取消大纲生成任务时出错: {str(e)}"
                # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                logger.error(error)
            # 更新项目状态为已取消
            config_db.status = ProjectConfigStatus.OUTLINE_CANCELED.value
            config_db.ai_generated_outline = None
            await config_db.save()
        elif flag == 'report':

            project_content = report_content_manager.get_project_content(project_id)
                
            # 如果存在异步任务，尝试取消
            if project_content and project_content.asyncioInstance:
                try:
                    project_content.asyncioInstance.cancel()
                    # error = f"已取消大纲生成任务"
                    logger.info(f"已取消项目 {project_id} 的报告生成任务")
                    # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                except Exception as e:
                    error = f"取消报告生成任务时出错: {str(e)}"
                    # outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                    logger.error(error)
                # 更新项目状态为已取消
                config_db.status = ProjectConfigStatus.REPORT_CANCELED.value
                config_db.ai_generated_report = None
                await config_db.save()

# 生成项目大纲接口
@router.post("/{project_id}/generate-outline", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_outline(
    project_id: str,
    request: Request
):
    # 检查用户报告使用次数限制
    current_user = get_current_user_from_state(request)
    user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
    if not user_obj:
        return send_data(False, None, "用户不存在")
    try:
        await check_user_usage_limit(user_obj.id)
    except Exception as e:
        return send_data(False, None, str(e))  
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("leader", "user", "model").first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, None, "无权访问此项目")
        # return send_data(False, None, f"读取大纲文件失败: {str(e)}")
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
    firstModel = config_db.model
    apiKey = firstModel.api_key
    apiUrl = firstModel.api_url
    model = firstModel.model_name
    await remove_storage(project_id=project_id)

    # 重新计算预估时间
    try:
        logger.info(f"项目 {config_db.id} 预估时间重新计算开始")
        estimated_time = await update_estimated_time(config_db, UpdateEstimatedTimeProcessingMode.GENERATE_OUTLINE)
        config_db.estimated_time = estimated_time
        config_db.updated_at = datetime.now()
        await config_db.save()
        logger.info(f"项目 {config_db.id} 预估时间重新计算完成: {config_db.estimated_time}")
    except Exception as e:
        logger.warning(f"项目 {config_db.id} 预估时间重新计算失败: {e}")

    # 获取config_response用于生成提示词
    config_response = await get_one_project_config(project_id)
    
    # 生成大纲提示词
    team_members = [
        ProjectMemberBase(
            name=join.member.name,
            title=join.member.title,
            representative_works=join.member.representative_works,
            organization=join.member.organization
        )
        for join in config_response.team_members
    ]
  
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"outline_{timestamp}.txt"
    if config_response.name:
        file_name = sanitize_filename(f"outline_{config_response.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    save_text_to_file(
        content="",
        file_path=relative_path
    )
    demo = ""
    try:
        if config_response.user_add_demo_id:
            file_data = await get_file_content_by_id(config_response.user_add_demo_id)
            file_path = file_data.file_path
            if file_path.endswith(".doc") or file_path.endswith(".docx"):
                demo = docx_file_to_markdown(file_path)
            else:
                demo = read_file_content(file_path)
    except Exception as e:
        error_msg = f"{ProjectConfigError.GET_USER_ADD_DEMO_FAIL.value}: {str(e)}"
        logger.error(error_msg)
        return send_data(False, None, error_msg)
    prompt = generate_outline_prompt(
      name=config_response.name or "",
      application_category=config_response.application_category or "",
      leader=config_response.leader,
      team_members=team_members,
      language_style=config_response.language_style or "",
      leader_introduction=config_response.ai_leader_introduction,
      demo=demo,
      user_prompt=config_response.user_add_prompt
    )
    # 调用LLM生成大纲 - 流式处理并保存到文件
    messages = [
        {"role": "system", "content": OUTLINE_SYSTEM_PROMPT},
        {"role": "user", "content": prompt}
    ]
    async def complete_callback(open_router_id: str, all_content: str): # 存储大纲内容
        config_db.status = ProjectConfigStatus.OUTLINE_GENERATED.value
        config_db.outline_generated_time = datetime.now()
        # 生成大纲的时候要把之前的内容清空
        config_db.manual_modified_outline = None
        config_db.manual_modified_outline_time = None
        config_db.ai_generated_report = None
        config_db.report_generation_time = None
        config_db.manual_modified_report_time = None
        config_db.manual_modified_report = None
        save_text_to_file(handle_before_save(remove_markdown_h1_and_text(all_content, config_db.name)), relative_path)
        # outline_content_manager.clear_project(project_id)
        # outline_content_manager.save_all_content_to_file(project_id, relative_path)

        await create_workflow(
            project_id=project_id,
            content=relative_path,
            name="GENERATED_OUTLINE_FIRST_TIME",
            current_user=current_user
        )
        try:
            temp = await get_generation_result(
                generation_id = open_router_id,
                api_key=apiKey
            )
            config_db.outline_tokens_consumed = temp.data.tokens_completion + temp.data.tokens_prompt
        except Exception as e:
            logger.error(f"{e}")
            config_db.outline_tokens_consumed = 0
        await config_db.save()
        
        # 更新用户使用次数
        # user_report_usage.used_count += 1
        # await user_report_usage.save()
    async def error_callback(error: str):
        # 处理错误
        logger.error(f"生成大纲时发生错误: {error}")
        outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
        config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
        await config_db.save()
    async def callback(content: str):
      outline_content_manager.add_content(project_id, content, ContentStatus.NORMAL)
    # 添加一个空字符串，用于触发流式处理
    outline_content_manager.add_content(project_id, "", ContentStatus.NORMAL)
    # 创建异步任务并存储
    task = asyncio.create_task(stream_llm_and_save(
        messages=messages, 
        model=model,
        apiKey=apiKey,
        apiUrl=apiUrl,
        user = current_user,
        flag = CallLLMFlag.GENERATE_OUTLINE.value,
        callback=callback,
        complete_callback=complete_callback,
        error_callback=error_callback,
        related_id = config_db.id
    ))
    # 将任务实例保存到内容管理器
    outline_content_manager.add_asyncio(project_id, task)
    
    # 更新项目配置 - 使用数据库模型
    config_db.ai_generated_outline = relative_path  # 存储大纲内容
    config_db.status = ProjectConfigStatus.OUTLINE_GENERATING.value
    await config_db.save()
    
    return send_data(True, await get_one_project_config(project_id))

# 生成项目报告接口
@router.post("/{project_id}/generate-report", response_model=ResponseModel[ProjectConfigResponse2])
async def generate_project_report(
    project_id: str,
    request: Request,
    data: Optional[GenerateConfig] = None
):
    summary_literature = data.summary_literature if data else False
    current_user = get_current_user_from_state(request)
    starttime = datetime.now()
    logger.info(f"报告开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    is_diff_count = False
    organization = None
    
    # 检查用户报告使用次数限制
    user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
    if not user_obj:
        return send_data(False, None, "用户不存在")
    
    try:
        await check_user_usage_limit(user_obj.id)
    except Exception as e:
        return send_data(False, None, str(e)) 
    # 获取用户使用记录（后续更新用）
    user_report_usage = await UserReportUsage.filter(user_id_id=current_user.id, is_deleted=False).first()
    # 判断是否具有机构
    # if current_user.organization:
    #     organization = await Organizations.filter(id=current_user.organization.id, is_deleted=False, is_active=True).first()
    #     if not organization:
    #         return send_data(False, None, "机构不存在或未激活")
    #     elif organization.limit_count <= organization.use_count:
    #         return send_data(False, None, "机构的可用次数已经用完。")
    # 直接从数据库获取ProjectConfig模型
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("leader", "user", "model").first()
    # config_db = ProjectConfigResponse2.model_validate(result)
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    # print(config_db, 'config_db')
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, None, "无权访问此项目")
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
   
    firstModel = config_db.model
    apiKey = firstModel.api_key
    apiUrl = firstModel.api_url
    model = firstModel.model_name
    await remove_storage(project_id=project_id, flag='report')
    # 获取config_response用于生成提示词
    config_response = await get_one_project_config(project_id)
    
    # 检查是否已有大纲
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
      return send_data(False, None, "请先生成项目大纲")
    outline: str = ""
    # 从文件中读取大纲内容
    try:
        full_path = os.path.join(os.getcwd(), outline_path)
        if not os.path.exists(full_path):
            return send_data(False, None, f"大纲文件不存在: {outline_path}")
            
        with open(full_path, "r", encoding="utf-8") as f:
            outline = f.read()
            
        if not outline:
            return send_data(False, None, "大纲文件内容为空")
    except Exception as e:
        return send_data(False, None, f"读取大纲文件失败: {str(e)}")
    # 创建一个研究实例来处理报告生成
    # 注意：不再使用api_key_id参数
    research = await Research.create(
        query=config_db.name,
        search_queries=[],
        contexts=[],
        status=ResearchStatus.PENDING
    )
    config_db.research = research
    await config_db.save()
    await config_db.refresh_from_db()
    # 生成报告提示词
    # prompt = generate_report_prompt(
    #     name=config_response.name or "",
    #     application_category=config_response.application_category or "",
    #     leader=config_response.leader,
    #     team_members=team_members,
    #     word_count_requirement=config_response.word_count_requirement or 0,
    #     outline=outline
    # )
    #print(prompt)
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"report_{timestamp}.txt"
    if config_response.name:
        file_name = sanitize_filename(f"report_{config_response.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    
    save_text_to_file(
        content="",
        file_path=relative_path
    )
    # # 获取绝对路径
    # abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # # 确保目录和文件存在
    # abs_folder_path = os.path.dirname(abs_file_path)
    # if not os.path.exists(abs_folder_path):
    #     os.makedirs(abs_folder_path)
    # if not os.path.exists(abs_file_path):
    #     with open(abs_file_path, 'w', encoding='utf-8') as f:
    #         f.write('')

    # 重新计算预估时间
    try:
        logger.info(f"项目 {config_db.id} 预估时间重新计算开始")
        estimated_time = await update_estimated_time(config_db, UpdateEstimatedTimeProcessingMode.GENERATE_CONTENT)
        config_db.estimated_time = estimated_time
        config_db.updated_at = datetime.now()
        await config_db.save()
        logger.info(f"项目 {config_db.id} 预估时间重新计算完成: {config_db.estimated_time}")
    except Exception as e:
        logger.warning(f"项目 {config_db.id} 预估时间重新计算失败: {e}")
        
    async def complete_callback(open_router_id: str, all_content: str): # 存储大纲内容
        await create_workflow(
            project_id=project_id,
            content=relative_path,
            name="GENERATED_CONTENT_FIRST_TIME",
            current_user=current_user
        )
        logger.info("准备把正文写到文件里面了。")
        report_content_manager.clear_project(project_id)
        logger.info(all_content)
        save_text_to_file(handle_before_save(remove_markdown_h1_and_text(all_content, config_db.name)), relative_path)
        logger.info("正文已经写到文件里面了。")
        # report_content_manager.save_all_content_to_file(project_id, relative_path)
        # print()
        try:
            temp = await get_generation_result(
                generation_id=open_router_id,
                api_key=apiKey
            )
            config_db.report_tokens_consumed = temp.data.tokens_completion + temp.data.tokens_prompt
        except Exception as e:
            print(f"{e}")   
            config_db.report_tokens_consumed = 0
        # 增加机构的
        config_db.status = ProjectConfigStatus.REPORT_GENERATED.value
        config_db.report_generation_time = datetime.now()  # 确保使用不带时区的日期时间
        config_db.manual_modified_report_time = None
        config_db.manual_modified_report = None
        await config_db.save()
        endtime = datetime.now()
        logger.info(f"报告：{config_db.id}生成结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
       
    async def error_callback(error):
        # 处理错误
        print(f"生成报告时发生错误: {error}")
        report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
        config_db.status = ProjectConfigStatus.REPORT_FAILED.value
        await config_db.save()
    async def callback(content: str):
        nonlocal is_diff_count
        if not is_diff_count:
            logger.info("更新用户使用次数")
            # 更新用户使用次数
            user_report_usage.used_count += 1
            await user_report_usage.save()
            if organization:
                organization.use_count += 1
                await organization.save()
            is_diff_count = True
        report_content_manager.add_content(project_id, content, ContentStatus.NORMAL)
    # 添加一个空字符串，用于触发流式处理
    report_content_manager.add_content(project_id, "", ContentStatus.NORMAL)
    # report_content_manager.set_config(project_id, Config(
    #     open_literature_summary=open_literature_summary
    # ))
    # 创建异步任务并存储
    # task = asyncio.create_task(stream_llm_and_save(
    #     messages=messages, 
    #     callback=callback,
    #     complete_callback=complete_callback,
    #     error_callback=error_callback
    # ))
    # 创建异步任务
    task = asyncio.create_task(stream_research_report_content(
        current_user=current_user,
        research=research,
        config_response=config_response,
        complete_callback=complete_callback,
        callback=callback,
        error_callback=error_callback,
        api_key=apiKey,
        api_url=apiUrl,
        model=model,
        outline=outline,
        open_literature_summary=summary_literature
    ))
    
    # 将任务保存到内容管理器中
    report_content_manager.add_asyncio(project_id, task)
    # 更新项目配置
    config_db.ai_generated_report = relative_path
    config_db.status = ProjectConfigStatus.REPORT_GENERATING.value
    await config_db.save()
    
    return send_data(True, await get_one_project_config(project_id))

# 流式返回大纲内容的接口（SSE）
@router.get("/{project_id}/stream-outline")
async def stream_project_outline(
    project_id: str,
    request: Request
):
    """
    流式返回项目大纲内容（SSE格式）
    """
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        print("项目配置不存在")
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        print("无权访问此项目")
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '无权访问此项目', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        print('处于生成状态')
        async def stream_realtime_content():
            # 已读取的内容计数
            read_count = 0
            project_content = outline_content_manager.get_project_content(project_id)
            
            if not project_content:
                print("找不到正在生成的内容")
                config_db.status = ProjectConfigStatus.OUTLINE_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
            print('发送已有的内容', config_db.status)
            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
                chunk = outline_content_manager.read_next_chunk(project_id)
                if chunk:
                    print('发送新内容', chunk)
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(0.5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
            
            # 获取剩余未读取的内容
            project_content = outline_content_manager.get_project_content(project_id)
            if project_content and project_content.unread_chunks:
                for item in project_content.unread_chunks:
                    yield f"data: {json.dumps({'content': item.content, 'status': item.status})}\n\n"
                read_count += len(project_content.unread_chunks)
            
            
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    print('不处于生成状态')
    # 如果不是正在生成状态，则回退到从文件读取
    outline_path = config_db.manual_modified_outline or config_db.ai_generated_outline
    if not outline_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目尚未生成大纲', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    # 如果用户没有一直在页面等到流式结束就会导致内存里面的内容不会被清除
    outline_content_manager.clear_project(project_id)
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(outline_path, current_user), 
        media_type="text/event-stream"
    )

# 流式返回报告内容的接口（SSE）
@router.get("/{project_id}/stream-report")
async def stream_project_report(
    project_id: str,
    request: Request
):
    """
    流式返回项目报告内容（SSE格式）
    """
    current_user = get_current_user_from_state(request)
    logger.info(f"流式返回项目报告内容（SSE格式），项目ID: {project_id}")
    # 检查项目是否存在并验证权限
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '无权访问此项目', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 检查正在生成的内容
    if config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
        # 如果是体验用户
        print(f"是否是体验用户：{current_user.is_trial}")
        if current_user.is_trial:
            already_generated_text = send_trail_text(config_db.id, 'report')
            if already_generated_text:
                logger.info("体验用户的报告截断啦！")
                return StreamingResponse(
                    content=stream_fn(already_generated_text),
                    media_type="text/event-stream"
                )   
        async def stream_realtime_content():
            send_length = 0
            # 已读取的内容计数
            read_count = 0
            project_content = report_content_manager.get_project_content(project_id)
            
            if not project_content:
                config_db.status = ProjectConfigStatus.REPORT_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                return  
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                print(f"已经读取过的：{chunk.content}")
                send_length += calculate_length(chunk)
                yield yield_self(
                    chunk,
                    send_length,
                    current_user
                )
                # await asyncio.sleep(0.02)
            
            # 然后持续检查是否有新内容
            while config_db.status == ProjectConfigStatus.REPORT_GENERATING.value:
                chunk = report_content_manager.read_next_chunk(project_id)
                if chunk:
                    send_length += calculate_length(chunk)
                    yield yield_self(
                        chunk,
                        send_length,
                        current_user
                    )
                    read_count += 1
                else:
                    # 暂时没有新内容，等待一会再检查
                    await asyncio.sleep(5)
                    # 重新获取数据库状态，检查是否仍在生成中
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
            
            # 获取剩余未读取的内容
            project_content = report_content_manager.get_project_content(project_id)
            if project_content and project_content.unread_chunks:
                for chunk in project_content.unread_chunks:
                    print('发送剩余未读内容', chunk)
                    yield yield_self(
                        chunk=chunk,
                        send_length=send_length,
                        current_user=current_user
                    )
                read_count += len(project_content.unread_chunks)
            logger.info("\n\n\n我要清空本地数据啦！\n\n\n")
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
            report_content_manager.clear_project(project_id)
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    
    # 如果不是正在生成状态，则回退到从文件读取
    report_path = config_db.manual_modified_report or config_db.ai_generated_report
    if not report_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '项目生成报告失败', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    logger.info("\n\n\n我要清空本地数据啦2！\n\n\n")
    # 如果用户没有一直在页面等到流式结束就会导致内存里面的内容不会被清除
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(report_path, current_user, True), 
        media_type="text/event-stream",
        background=BackgroundTask(lambda: report_content_manager.clear_project(project_id))
    )

@router.post("/{project_id}/stop-outline", response_model=ResponseModel[bool])
async def stop_outline_generation(
    project_id: str, 
    request: Request
):
    """终止项目大纲生成"""
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return send_data(False, False, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, False, "无权访问此项目")
    
    # 检查当前状态
    if config_db.status != ProjectConfigStatus.OUTLINE_GENERATING.value:
        return send_data(False, False, "当前没有正在生成大纲")
    
    try:
        # 获取该项目的内容对象
        project_content = outline_content_manager.get_project_content(project_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消大纲生成任务"
                print(f"已取消项目 {project_id} 的大纲生成任务")
                outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
            except Exception as e:
                error = f"取消大纲生成任务时出错: {str(e)}"
                outline_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                print(error)
    
        
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.OUTLINE_CANCELED.value
        config_db.ai_generated_outline = None
        await config_db.save()
        
        return send_data(True, True, "已终止大纲生成")
    except Exception as e:
        return send_data(False, False, f"终止大纲生成失败: {str(e)}")

@router.post("/{project_id}/stop-report", response_model=ResponseModel[bool])
async def stop_report_generation(
    project_id: str,
    request: Request
):
    """终止项目报告生成"""
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related("user").first()
    if not config_db:
        return send_data(False, False, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        return send_data(False, False, "无权访问此项目")
    
    # 检查当前状态
    if config_db.status != ProjectConfigStatus.REPORT_GENERATING.value:
        return send_data(False, False, "当前没有正在生成报告")
    
    try:
        # 获取该项目的内容对象
        project_content = report_content_manager.get_project_content(project_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消报告生成任务"
                report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                print(f"已取消项目 {project_id} 的报告生成任务")
            except Exception as e:
                error = f"取消报告生成任务时出错: {str(e)}"
                report_content_manager.add_content(project_id, error, ContentStatus.ERROR)
                # 记录错误信息
                print(f"取消报告生成任务时出错: {str(e)}")
        
        # 更新项目状态为已取消
        config_db.status = ProjectConfigStatus.REPORT_CANCELED.value
        config_db.report_generation_time = None
        config_db.ai_generated_report = None
        await config_db.save()
        
        return send_data(True, True, "已终止报告生成")
    except Exception as e:
        return send_data(False, False, f"终止报告生成失败: {str(e)}")
