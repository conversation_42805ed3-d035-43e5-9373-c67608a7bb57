from fastapi import APIRouter, Depends
from app.api.routes import (
  auth,
  file_biz_relations,
  requirements_attachment_files,
  users,
  project_configs,
  project_leaders,
  project_members,
  project_member_joins,
  model_configs,
  project_report,
  project_downloads,
  outline,
  user_report_usages,
  workflow,
  project_model_config,
  dictionary,
  area,
  # research,
  organizations,
  menu,
  role,
  organization_role_menu,
  insight,
  organization_menu,
  project_verify,
  project_url_summary,
  organization_model,
  user_default_model,
  upload_file
  # literatures
)
from app.api.deps import (
  get_current_user,
  auth_super_admin_depend,
  auth_not_trial_depend
)



api_router = APIRouter(prefix="/api")
auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user)])
super_admin_auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user), Depends(auth_super_admin_depend)])
# 非体验用户才有的权限路由
no_trial_auth_router = APIRouter(prefix="", dependencies=[Depends(get_current_user), Depends(auth_not_trial_depend)])

# 添加各模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
auth_router.include_router(role.router, prefix="/role", tags=["角色管理"])
auth_router.include_router(menu.router, prefix="/menu", tags=["菜单管理"])
auth_router.include_router(organization_menu.router, prefix="/organization-menu", tags=["机构菜单管理"])
auth_router.include_router(organization_role_menu.router, prefix="/role-menu", tags=["角色菜单管理"]) 
auth_router.include_router(users.router, prefix="/users", tags=["用户管理"])
auth_router.include_router(project_configs.router, prefix="/project-configs", tags=["项目配置"])
auth_router.include_router(project_leaders.router, prefix="/project-leaders", tags=["项目主体"]) 
auth_router.include_router(project_members.router, prefix="/project-members", tags=["项目成员"])
auth_router.include_router(project_member_joins.router, prefix="/project-member-joins", tags=["项目成员关联"])
auth_router.include_router(project_report.router, prefix="/project-reports", tags=["项目大纲报告生成"])
auth_router.include_router(requirements_attachment_files.router, prefix="/requirements-attachments-files", tags=["申报要求附件文件"])
auth_router.include_router(file_biz_relations.router, prefix="/file-biz-relations", tags=["文件关联管理"])
auth_router.include_router(user_report_usages.router, tags=["用户报告使用次数"])
auth_router.include_router(workflow.router, prefix="/workflows", tags=["工作流程"], deprecated=True)
super_admin_auth_router.include_router(model_configs.router, prefix="/model-configs", tags=["模型配置管理"]) 
auth_router.include_router(outline.router, prefix="/text-handle", tags=["文本处理"])
auth_router.include_router(project_model_config.router, prefix="/project-model-configs", tags=["项目模型配置关联"])
auth_router.include_router(dictionary.router, prefix="/dictionary", tags=["字典"])
auth_router.include_router(area.router, prefix="/area", tags=["省市区"])
auth_router.include_router(project_url_summary.router, prefix="/project-url", tags=["用户添加文献网站"])
super_admin_auth_router.include_router(organizations.router, prefix="/organizations", tags=["机构管理"])
auth_router.include_router(insight.router, prefix="/insight")
auth_router.include_router(project_downloads.router, prefix="/project-downloads", tags=["项目文件下载"])
auth_router.include_router(project_verify.router, prefix="/project-verify", tags=["幻觉审查和去AI痕迹"])
auth_router.include_router(organization_model.router, prefix="/organization-model", tags=["机构的模型信息"]) 
auth_router.include_router(user_default_model.router, prefix="/user-model", tags=["用户的模型"]) 
auth_router.include_router(upload_file.router, prefix="/upload-file", tags=["上传文件"])
# no_trial_auth_router.include_router(project_downloads.router, prefix="/project-downloads", tags=["项目文件下载"])
# auth_router.include_router(literatures.router, prefix="/literatures", tags=["文献管理"]) 
api_router.include_router(auth_router)
api_router.include_router(super_admin_auth_router)
api_router.include_router(no_trial_auth_router)
# 要加上体验用户限制权限的接口