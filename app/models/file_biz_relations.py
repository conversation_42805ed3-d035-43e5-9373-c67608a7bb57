import uuid
from tortoise import fields
from tortoise.models import Model
from app.utils.constants import ProductType


class FileBizRelation(Model):
    """文件和业务关联表 - 用于保存各产品线的文件和业务信息"""
    
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    
    # 关联的文件记录
    file = fields.ForeignKeyField(
        "models.RequirementsAttachmentFiles",
        related_name="file_biz_relations",
        description="关联的文件"
    )
    
    # AI分析总结信息（可能很长）
    ai_analysis_summary = fields.TextField(
        null=True, 
        description="AI分析总结信息（来自LLM的详细分析结果）"
    )
    
    # 产品类型
    product_type = fields.CharEnumField(
        ProductType,
        description="产品类型（INSIGHTPLUS/DOCGEN/LABPLAN等）"
    )
    
    # 业务ID（不做外键关联，只保存ID字符串）
    biz_id = fields.CharField(
        max_length=100,
        null=True,
        description="关联的业务ID（项目ID、实验ID等，不做外键约束）"
    )
    
    # 时间字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "file_biz_relations"
        description = "文件和业务关联表"
    
    def __str__(self):
        return f"{self.product_type}_{self.biz_id}_{self.file.file_name}" 